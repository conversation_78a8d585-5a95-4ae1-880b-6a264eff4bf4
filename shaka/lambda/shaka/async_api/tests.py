import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from app import app, authenticate_token


class TestAsyncAPI(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)

    def tearDown(self):
        app.dependency_overrides.clear()

    @patch('app.get_redis_client')
    def test_timeout_scenario(self, mock_redis):
        def mock_auth():
            return "valid_token_32_characters_long"
        app.dependency_overrides[authenticate_token] = mock_auth

        mock_redis_instance = MagicMock()
        mock_redis_instance.blpop.return_value = None
        mock_redis.return_value = mock_redis_instance

        response = self.client.get(
            "/api/v1/sim/event/status/1/test_iccid?timeout=1",
            headers={"Authorization": "Bearer valid_token_32_characters_long"}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "timeout"})

    @patch('app.get_redis_client')
    def test_successful_status_receive(self, mock_redis):
        def mock_auth():
            return "valid_token_32_characters_long"
        app.dependency_overrides[authenticate_token] = mock_auth

        mock_redis_instance = MagicMock()
        mock_redis_instance.blpop.return_value = ("1:valid_token_32_characters_long:sim:test_iccid:status", "installed")
        mock_redis.return_value = mock_redis_instance

        response = self.client.get(
            "/api/v1/sim/event/status/1/test_iccid?timeout=1",
            headers={"Authorization": "Bearer valid_token_32_characters_long"}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok", "data": "installed"})

    def test_invalid_token(self):
        response = self.client.get(
            "/api/v1/sim/event/status/1/test_iccid",
            headers={"Authorization": "Bearer invalid_token"}
        )
        self.assertEqual(response.status_code, 401)

    def test_missing_auth_header(self):
        response = self.client.get("/api/v1/sim/event/status/1/test_iccid")
        self.assertEqual(response.status_code, 403)

    @patch('app.get_redis_client')
    def test_health_check(self, mock_redis):
        mock_redis_instance = MagicMock()
        mock_redis_instance.ping.return_value = True
        mock_redis.return_value = mock_redis_instance
        response = self.client.get("/health")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "healthy", "redis": "connected"})

    def test_root_endpoint(self):
        response = self.client.get("/")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok", "service": "async-api"})

if __name__ == '__main__':
    unittest.main()
