# Async API

A lightweight FastAPI service for long-polling async interfaces.

## Overview

This service provides a long-polling API endpoint that waits for events using Redis BLPOP operations. It's designed to handle asynchronous operations without blocking server threads in the main application, providing an efficient way to wait for events with configurable timeouts.

## Features

- **Bearer Token Authentication**: Uses unguessable, unique tokens for secure access
- **Redis-based Messaging**: Uses Redis BLPOP for efficient long-polling
- **Configurable Timeouts**: Flexible timeout handling with no upper limit
- **Health Checks**: Built-in health check endpoints
- **Error Handling**: Comprehensive error handling and logging
- **Auto-generated Documentation**: FastAPI Swagger/OpenAPI docs available at `/docs`

## API Documentation

The complete API documentation is available through FastAPI's auto-generated Swagger UI:
- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`

This includes interactive documentation for all endpoints with request/response schemas.

## Environment Variables

- `REDIS_HOST`: Redis server host (default: localhost)
- `REDIS_PORT`: Redis server port (default: 6379)

## Architecture

### Redis Key Format

Events are stored in Redis with the key format:
```
{client_id}:{token}:sim:{iccid}:status
```

Where:
- `client_id`: Numeric identifier for the client (passed in URL path)
- `token`: Unguessable, unique authentication token (from Bearer header)
- `iccid`: The SIM ICCID being monitored
- `status`: Fixed suffix for the event type

### High-Level Flow

1. Client makes authenticated request to `/api/v1/sim/event/status/{client_id}/{iccid}`
2. Service validates the bearer token format (32 characters)
3. Service constructs Redis key using client_id, token, and iccid
4. Service performs Redis BLPOP operation with specified timeout
5. If event received, returns event data; if timeout, returns timeout status

## Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the server:
   ```bash
   uvicorn async_api.app:app --reload
   ```

3. Run tests:
   ```bash
   python -m unittest tests.py
   ```

4. Run linting:
   ```bash
   flake8 .
   ```

## Deployment

This service is designed for AWS App Runner deployment using the included Procfile.
