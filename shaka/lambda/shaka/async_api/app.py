import logging
import async<PERSON>
from fastapi import FastAP<PERSON>, HTTPException, Depends, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from redis_client import get_redis_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Async API",
    description="A lightweight FastAPI service for long-polling async interfaces",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

security = HTTPBearer()


class StatusResponse(BaseModel):
    status: str
    data: str = None


class TimeoutResponse(BaseModel):
    status: str


async def authenticate_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    token = credentials.credentials
    if not token or len(token) != 32:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format"
        )
    return token


@app.get(
    "/api/v1/sim/event/status/{client_id}/{iccid}",
    response_model=StatusResponse | TimeoutResponse,
    summary="Wait for SIM status event",
    description="Long-poll for a SIM status event using Redis BLPOP. Returns immediately if an event is available, otherwise waits up to the specified timeout."
)
async def wait_for_sim_status(
    client_id: int,
    iccid: str,
    timeout: int = Query(default=30, ge=1, description="Timeout in seconds (minimum 1)"),
    token: str = Depends(authenticate_token)
):
    redis_client = await get_redis_client()
    key = f"{client_id}:{token}:sim:{iccid}:status"

    try:
        logger.info(f"Waiting for status on key: {key} with timeout: {timeout}")
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: redis_client.blpop(key, timeout=timeout)
        )

        if result:
            _key, value = result
            logger.info(f"Received status update for {key}: {value}")
            return StatusResponse(status="ok", data=value)
        else:
            logger.info(f"Timeout waiting for status on key: {key}")
            return TimeoutResponse(status="timeout")

    except Exception as e:
        logger.error(f"Redis error for key {key}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Redis error: {str(e)}"
        )


class HealthResponse(BaseModel):
    status: str
    redis: str


@app.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check",
    description="Check the health status of the service and its Redis connection"
)
async def health_check():
    try:
        redis_client = await get_redis_client()
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, redis_client.ping)
        return HealthResponse(status="healthy", redis="connected")
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )


@app.get(
    "/",
    summary="Root endpoint",
    description="Basic status endpoint for AWS App Runner compatibility"
)
async def root():
    return {"status": "ok", "service": "async-api"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
