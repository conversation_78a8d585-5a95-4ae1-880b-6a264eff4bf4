import logging
import redis
from django.conf import settings
from redis.exceptions import RedisError

logger = logging.getLogger(__name__)

_redis_client = None


def get_redis_client():
    global _redis_client
    if _redis_client is None:
        _redis_client = redis.Redis(
            host=getattr(settings, 'ASYNC_API_REDIS_HOST', 'localhost'),
            port=getattr(settings, 'ASYNC_API_REDIS_PORT', 6379),
            db=0,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
    return _redis_client


class AsyncAPIClient:
    def __init__(self):
        self.redis_client = get_redis_client()

    def set_sim_status(self, client_id, token, iccid, status):
        try:
            key = f"{client_id}:{token}:sim:{iccid}:status"
            result = self.redis_client.lpush(key, status)
            logger.info("Set SIM status for key %s: %s", key, status)
            return result > 0
        except RedisError as redis_error:
            logger.error(
                "Failed to set SIM status for %s:%s:sim:%s:status - %s",
                client_id, token, iccid, redis_error
            )
            return False

    def test_connection(self):
        try:
            return self.redis_client.ping()
        except RedisError as redis_error:
            logger.error("Redis connection test failed: %s", redis_error)
            return False

SimStatusClient = AsyncAPIClient
